import Phaser from 'phaser';

export type BirdPreset = {
  key: string;
  spriteKey: string;  // sprite name in atlas
  color: number;      // fallback color if sprite not found
  width: number;
  height: number;
};

export class SelectBirdScene extends Phaser.Scene {
  private presets: BirdPreset[] = [
    { key: 'canary', spriteKey: 'canary', color: 0xffd166, width: 80, height: 60 },   // default
    { key: 'sky', spriteKey: 'sky', color: 0x8ecae6, width: 72, height: 56 },
    { key: 'lime', spriteKey: 'lime', color: 0x90be6d, width: 84, height: 60 },
    { key: 'rose', spriteKey: 'rose', color: 0xef476f, width: 72, height: 64 },
  ];

  private titleText?: Phaser.GameObjects.Text;
  private subtitleText?: Phaser.GameObjects.Text;
  private hintText?: Phaser.GameObjects.Text;
  private choiceContainers: Phaser.GameObjects.Container[] = [];
  private onResizeHandler?: (size: Phaser.Structs.Size) => void;

  constructor() {
    super('SelectBirdScene');
  }

  preload() {
    // Load game atlas
    this.load.atlas('gameAtlas', 'images/game_atlas.png', 'images/game_atlas.json');
  }

  create() {
    const { width, height } = this.scale;

    // Background
    this.add.rectangle(width / 2, height / 2, width, height, 0x0c1323);

    // Title
    this.titleText = this.add.text(width / 2, 56, 'Select Your Bird', {
      fontFamily: 'monospace', fontSize: '28px', color: '#ffffff'
    }).setOrigin(0.5);

    // Description
    this.subtitleText = this.add.text(width / 2, 92, 'Tap a bird to start', {
      fontFamily: 'monospace', fontSize: '14px', color: '#8ecae6'
    }).setOrigin(0.5);

    // Load last selection from localStorage if any
    const savedKey = localStorage.getItem('flappy:selectedBirdKey');
    const saved = this.presets.find(p => p.key === savedKey);
    if (saved) {
      this.registry.set('selectedBird', saved);
    }

    // Build responsive grid
    this.layoutChoices();

    // Hint
    this.hintText = this.add.text(width / 2, height - 28, 'Press Enter to use selected / default', {
      fontFamily: 'monospace', fontSize: '12px', color: '#8ecae6'
    }).setOrigin(0.5);

    // Enter starts with current selected or default 0
    this.input.keyboard?.on('keydown-ENTER', () => {
      const selected: BirdPreset = this.registry.get('selectedBird') || this.presets[0];
      this.selectAndStart(selected);
    });

    // Handle responsive resize
    this.onResizeHandler = (gameSize: Phaser.Structs.Size) => {
      const { width: w, height: h } = gameSize;
      this.cameras.main.setSize(w, h);
      this.redrawLayout();
    };
    this.scale.on('resize', this.onResizeHandler);
    this.events.once('shutdown', () => {
      if (this.onResizeHandler) this.scale.off('resize', this.onResizeHandler);
    });
  }

  private selectAndStart(preset: BirdPreset) {
    this.registry.set('selectedBird', preset);
    localStorage.setItem('flappy:selectedBirdKey', preset.key);
    this.scene.start('PlayScene');
  }

  private layoutChoices() {
    // clear old
    this.choiceContainers.forEach(c => c.destroy());
    this.choiceContainers = [];

    const { width, height } = this.scale;

    // Determine grid columns based on width
    let cols = 4;
    if (width < 360) cols = 1;
    else if (width < 520) cols = 2;
    else if (width < 760) cols = 3;

    const rows = Math.ceil(this.presets.length / cols);
    const areaTop = 140;
    const areaBottom = height - 80;
    const areaHeight = Math.max(120, areaBottom - areaTop);
    const spacingY = Math.min(140, Math.max(90, Math.floor(areaHeight / Math.max(1, rows))));
    const spacingX = Math.min(160, Math.max(100, Math.floor(width / Math.max(2, cols + 1))));

    // Scale items for small screens
    let itemScale = 1.0;
    if (width < 360) itemScale = 2.0; // make them bigger and easier to tap
    else if (width < 520) itemScale = 1.6;
    else if (width < 760) itemScale = 1.3;

    const startX = width / 2 - ((cols - 1) * spacingX) / 2;
    const startY = areaTop + Math.max(0, (areaHeight - (rows - 1) * spacingY) / 2);

    this.presets.forEach((preset, i) => {
      const col = i % cols;
      const row = Math.floor(i / cols);
      const x = startX + col * spacingX;
      const y = startY + row * spacingY;

      const container = this.add.container(x, y).setScale(itemScale);

      // Create bird sprite from atlas
      const birdSprite = this.add.sprite(0, 0, 'gameAtlas', preset.spriteKey);
      birdSprite.setDisplaySize(preset.width, preset.height);

      // Add border effect
      const border = this.add.rectangle(0, 0, preset.width + 4, preset.height + 4, 0x000000, 0)
        .setStrokeStyle(2, 0xffffff);

      const label = this.add.text(0, 36, preset.key, {
        fontFamily: 'monospace', fontSize: '14px', color: '#ffffff'
      }).setOrigin(0.5);

      container.add([border, birdSprite, label]);

      // Larger touch-friendly hit box (not scaled by container)
      const hit = this.add.rectangle(x, y, Math.max(96, preset.width * itemScale + 32), 96, 0x000000, 0)
        .setInteractive({ useHandCursor: true });
      // Hover effect (web/desktop)
      hit.on('pointerover', () => border.setStrokeStyle(2, 0xffff99));
      hit.on('pointerout', () => border.setStrokeStyle(2, 0xffffff));
      hit.on('pointerdown', () => this.selectAndStart(preset));

      // Keep container and hit synchronized on resize by storing both
      (container as any).__hitRef = hit;

      this.choiceContainers.push(container);
    });
  }

  private redrawLayout() {
    const { width, height } = this.scale;
    // Update background color fill to cover new size
    this.add.rectangle(width / 2, height / 2, width, height, 0x0c1323).setDepth(-1);

    this.titleText?.setPosition(width / 2, 56);
    this.subtitleText?.setPosition(width / 2, 92);
    this.hintText?.setPosition(width / 2, height - 28);

    // Rebuild layout fully for simplicity and correctness
    // Destroy old hit refs
    this.choiceContainers.forEach(c => {
      const hit: Phaser.GameObjects.Rectangle | undefined = (c as any).__hitRef;
      hit?.destroy();
    });
    this.layoutChoices();
  }
}
