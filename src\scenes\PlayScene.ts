import Phaser from 'phaser';
import type { BirdPreset } from './SelectBirdScene';

export class PlayScene extends Phaser.Scene {
  private bird!: Phaser.GameObjects.Rectangle & { body: Phaser.Physics.Arcade.Body };
  private pipes!: Phaser.Physics.Arcade.Group;
  private score = 0;
  private scoreText!: Phaser.GameObjects.Text;
  private isGameOver = false;
  private spaceKey?: Phaser.Input.Keyboard.Key;
  private upKey?: Phaser.Input.Keyboard.Key;
  private wKey?: Phaser.Input.Keyboard.Key;
  private hintText?: Phaser.GameObjects.Text;
  private gameOverText?: Phaser.GameObjects.Text;
  private restartText?: Phaser.GameObjects.Text;
  private backToSelectText?: Phaser.GameObjects.Text;
  private gameOverPanel?: Phaser.GameObjects.Graphics;
  private spawnEvent?: Phaser.Time.TimerEvent;
  private cleanupEvents: Phaser.Time.TimerEvent[] = [];

  private PIPE_SPEED = -200;
  private PIPE_GAP = 140;
  private PIPE_SPAWN_INTERVAL = 1500; // ms

  constructor() {
    super('PlayScene');
  }

  create() {
    // Reset state on (re)start
    this.isGameOver = false;
    this.physics.world.resume(); // ensure physics running after previous pause
    this.time.timeScale = 1;     // resume timers after previous freeze
    const { width, height } = this.scale;

    // Background color overlay (optional aesthetic)
    this.add.rectangle(width / 2, height / 2, width, height, 0x0c1323);

    // Bird (use selected preset from registry, fallback to default)
    const fallback: BirdPreset = { key: 'canary', color: 0xffd166, width: 80, height: 60 };
    const preset = (this.registry.get('selectedBird') as BirdPreset) || fallback;
    const birdWidth = preset.width;
    const birdHeight = preset.height;
    const birdRect = this.add.rectangle(100, height / 2, birdWidth, birdHeight, preset.color);
    this.physics.add.existing(birdRect);
    this.bird = birdRect as any;
    this.bird.body.setCollideWorldBounds(true);
    const radius = Math.min(birdWidth, birdHeight) / 2;
    this.bird.body.setCircle(radius);
    // Center the circular body within the rectangle to avoid early bound collisions
    this.bird.body.setOffset(birdWidth / 2 - radius, birdHeight / 2 - radius);
    this.bird.body.setAllowGravity(true); // ensure gravity re-enabled on fresh scene
    this.bird.body.setVelocity(0, 0);      // clear any previous velocity

    // Pipes group (static in gravity, but moving via velocity)
    this.pipes = this.physics.add.group({ allowGravity: false, immovable: true });

    // Score UI
    this.score = 0;
    this.scoreText = this.add.text(width / 2, 40, '0', {
      fontFamily: 'monospace',
      fontSize: '32px',
      color: '#ffffff'
    }).setOrigin(0.5);

    // Controls
    this.input.on('pointerdown', this.flap, this);
    // Capture keys to stop browser default and ensure Phaser processes them first
    this.input.keyboard?.addCapture([
      Phaser.Input.Keyboard.KeyCodes.SPACE,
      Phaser.Input.Keyboard.KeyCodes.UP,
      Phaser.Input.Keyboard.KeyCodes.W,
    ]);
    if (this.input.keyboard) {
      this.input.keyboard.enabled = true; // ensure keyboard plugin is active
    }
    this.spaceKey = this.input.keyboard?.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
    this.upKey = this.input.keyboard?.addKey(Phaser.Input.Keyboard.KeyCodes.UP);
    this.wKey = this.input.keyboard?.addKey(Phaser.Input.Keyboard.KeyCodes.W);
    // Fallback listeners in case JustDown misses due to focus timing
    this.input.keyboard?.on('keydown-SPACE', this.flap, this);
    this.input.keyboard?.on('keydown-UP', this.flap, this);
    this.input.keyboard?.on('keydown-W', this.flap, this);
    // Additional fallback on keyup (SPACE works at Game Over due to keyup; mirror that here)
    this.input.keyboard?.on('keyup-SPACE', this.flap, this);
    this.input.keyboard?.on('keyup-UP', this.flap, this);
    this.input.keyboard?.on('keyup-W', this.flap, this);

    // Collisions
    this.physics.add.collider(this.bird, this.pipes, this.gameOver, undefined, this);

    // Spawn pipes loop
    this.spawnEvent = this.time.addEvent({
      delay: this.PIPE_SPAWN_INTERVAL,
      loop: true,
      callback: () => this.spawnPipePair(),
    });

    // Hint text
    this.hintText = this.add.text(width / 2, height - 24, 'Tap / Space to flap', {
      fontFamily: 'monospace', fontSize: '14px', color: '#8ecae6'
    }).setOrigin(0.5);

    // Initial bounds + resize handling
    this.updateBounds(width, height);
    const onResize = (gameSize: Phaser.Structs.Size) => {
      this.updateBounds(gameSize.width, gameSize.height);
    };
    this.scale.on('resize', onResize);
    this.events.once('shutdown', () => {
      this.scale.off('resize', onResize);
    });
  }

  private flap() {
    if (this.isGameOver) return; // ignore flap during game over; restart handled in gameOver()
    this.bird.body.setVelocityY(-300);
  }

  private spawnPipePair() {
    const { width, height } = this.scale;

    // Randomize gap center
    const margin = 40;
    const minY = margin + this.PIPE_GAP / 2;
    const maxY = height - margin - this.PIPE_GAP / 2;
    const gapCenterY = Phaser.Math.Between(minY, maxY);

    // Top pipe
    const topHeight = gapCenterY - this.PIPE_GAP / 2;
    const top = this.add.rectangle(width + 40, topHeight / 2, 60, topHeight, 0x70c1b3);
    this.pipes.add(top);
    (top.body as Phaser.Physics.Arcade.Body).setVelocityX(this.PIPE_SPEED);

    // Bottom pipe
    const bottomHeight = height - (gapCenterY + this.PIPE_GAP / 2);
    const bottom = this.add.rectangle(width + 40, height - bottomHeight / 2, 60, bottomHeight, 0x70c1b3);
    this.pipes.add(bottom);
    (bottom.body as Phaser.Physics.Arcade.Body).setVelocityX(this.PIPE_SPEED);

    // Scoring sensor
    const sensor = this.add.rectangle(width + 70, gapCenterY, 10, this.PIPE_GAP, 0x000000, 0);
    this.physics.add.existing(sensor);
    const sensorBody = sensor.body as Phaser.Physics.Arcade.Body;
    sensorBody.setAllowGravity(false);
    sensorBody.setVelocityX(this.PIPE_SPEED);

    // Overlap for score
    const overlapCb = (_: any, s: any) => {
      if (!sensor.getData('scored')) {
        sensor.setData('scored', true);
        this.incrementScore();
      }
    };
    this.physics.add.overlap(this.bird, sensor as any, overlapCb, undefined, this);

    // Cleanup when off screen
    const cleanup = () => {
      [top, bottom, sensor].forEach(obj => obj.destroy());
    };

    const ev = this.time.addEvent({ delay: 10000, callback: cleanup });
    this.cleanupEvents.push(ev);
  }

  private incrementScore() {
    this.score += 1;
    this.scoreText.setText(String(this.score));
  }

  update() {
    if (this.isGameOver) return;
    // Space to flap (more reliable than event listener across focus states)
    const pressed =
      (this.spaceKey && Phaser.Input.Keyboard.JustDown(this.spaceKey)) ||
      (this.upKey && Phaser.Input.Keyboard.JustDown(this.upKey)) ||
      (this.wKey && Phaser.Input.Keyboard.JustDown(this.wKey));
    if (pressed) {
      this.flap();
    }
    // Game over if bird hits world bounds (top/bottom)
    const body = this.bird.body as Phaser.Physics.Arcade.Body;
    if (body.blocked.up || body.blocked.down) {
      this.gameOver();
      return;
    }
  }

  private gameOver = () => {
    if (this.isGameOver) return;
    this.isGameOver = true;

    // Stop all pipes
    this.pipes.children.iterate(child => {
      const body = (child as any).body as Phaser.Physics.Arcade.Body | undefined;
      body?.setVelocityX(0);
      return true;
    });

    // Stop spawning new pipes
    if (this.spawnEvent) {
      this.spawnEvent.remove(false);
      this.spawnEvent = undefined;
    }

    // Pause physics world to freeze any remaining motion
    this.physics.world.pause();

    // Cancel any scheduled cleanup so pipes stay on screen
    if (this.cleanupEvents.length) {
      this.cleanupEvents.forEach(e => e.remove(false));
      this.cleanupEvents = [];
    }

    // Freeze timers entirely to stop any residual time events
    this.time.timeScale = 0;

    // Freeze bird
    this.bird.body.setVelocity(0);
    this.bird.body.allowGravity = false;

    // Show Game Over
    const { width, height } = this.scale;
    // Panel background for contrast (rounded corners via Graphics)
    const panelW = Math.min(360, Math.max(260, Math.floor(width * 0.6)));
    const panelH = 130;
    this.gameOverPanel = this.add.graphics({ x: 0, y: 0 }).setDepth(0);
    this.drawRoundedPanel(this.gameOverPanel, width / 2, height / 2 + 20, panelW, panelH, 12);

    this.gameOverText = this.add.text(width / 2, height / 2 - 10, 'Game Over', {
      fontFamily: 'monospace', fontSize: '26px', color: '#ffffff', align: 'center'
    }).setOrigin(0.5).setDepth(1).setShadow(0, 2, '#000000', 4, true, true);

    // Buttons
    this.restartText = this.add.text(width / 2, height / 2 + 26, 'Restart (Space)', {
      fontFamily: 'monospace', fontSize: '18px', color: '#8ecae6'
    }).setOrigin(0.5).setInteractive({ useHandCursor: true }).setDepth(1).setShadow(0, 2, '#000000', 4, true, true);
    this.backToSelectText = this.add.text(width / 2, height / 2 + 56, 'Back to Select (Enter)', {
      fontFamily: 'monospace', fontSize: '18px', color: '#8ecae6'
    }).setOrigin(0.5).setInteractive({ useHandCursor: true }).setDepth(1).setShadow(0, 2, '#000000', 4, true, true);

    this.restartText.on('pointerover', () => this.restartText?.setColor('#ffffff'));
    this.restartText.on('pointerout', () => this.restartText?.setColor('#8ecae6'));
    this.backToSelectText.on('pointerover', () => this.backToSelectText?.setColor('#ffffff'));
    this.backToSelectText.on('pointerout', () => this.backToSelectText?.setColor('#8ecae6'));

    this.restartText.on('pointerup', () => this.scene.restart());
    this.backToSelectText.on('pointerup', () => this.scene.start('SelectBirdScene'));

    // Keyboard shortcuts
    this.input.keyboard?.once('keyup-SPACE', () => this.scene.restart());
    this.input.keyboard?.once('keyup-ENTER', () => this.scene.start('SelectBirdScene'));
    this.input.keyboard?.once('keyup-ESC', () => this.scene.start('SelectBirdScene'));
  };

  private updateBounds(width: number, height: number) {
    // Update physics world bounds to match new size
    this.physics.world.setBounds(0, 0, width, height);

    // Reposition UI elements
    this.scoreText.setPosition(width / 2, 40);
    this.hintText?.setPosition(width / 2, height - 24);
    if (this.gameOverText) this.gameOverText.setPosition(width / 2, height / 2 - 10);
    if (this.restartText) this.restartText.setPosition(width / 2, height / 2 + 26);
    if (this.backToSelectText) this.backToSelectText.setPosition(width / 2, height / 2 + 56);
    if (this.gameOverPanel) {
      const panelW = Math.min(360, Math.max(260, Math.floor(width * 0.6)));
      const panelH = 130;
      this.gameOverPanel.clear();
      this.drawRoundedPanel(this.gameOverPanel, width / 2, height / 2 + 20, panelW, panelH, 12);
    }
  }

  private drawRoundedPanel(g: Phaser.GameObjects.Graphics, cx: number, cy: number, w: number, h: number, r: number) {
    const x = cx - w / 2;
    const y = cy - h / 2;
    g.fillStyle(0x000000, 0.6);
    g.lineStyle(2, 0xffffff, 0.15);
    g.fillRoundedRect(x, y, w, h, r);
    g.strokeRoundedRect(x, y, w, h, r);
  }
}
